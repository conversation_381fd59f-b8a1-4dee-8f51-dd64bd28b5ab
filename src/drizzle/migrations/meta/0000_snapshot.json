{"version": "5", "dialect": "mysql", "id": "eae24f1e-2556-43bb-a506-c51d4a607c61", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"applications": {"name": "applications", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "job_id": {"name": "job_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "current_stage_id": {"name": "current_stage_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "candidate": {"name": "candidate", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "can_contact": {"name": "can_contact", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"applications_job_id_job_listing_id_fk": {"name": "applications_job_id_job_listing_id_fk", "tableFrom": "applications", "tableTo": "job_listing", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "applications_candidate_candidate_id_fk": {"name": "applications_candidate_candidate_id_fk", "tableFrom": "applications", "tableTo": "candidate", "columnsFrom": ["candidate"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"applications_id": {"name": "applications_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "attachments": {"name": "attachments", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "file_url": {"name": "file_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "candidate_id": {"name": "candidate_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "attachment_type": {"name": "attachment_type", "type": "enum('RESUME','COVER_LETTER','OFFER_LETTER','OTHER')", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"attachments_candidate_id_candidate_id_fk": {"name": "attachments_candidate_id_candidate_id_fk", "tableFrom": "attachments", "tableTo": "candidate", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"attachments_id": {"name": "attachments_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "candidate": {"name": "candidate", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "cv_path": {"name": "cv_path", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "enum('Active','Rejected','Hired')", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'Active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"candidate_id": {"name": "candidate_id", "columns": ["id"]}}, "uniqueConstraints": {"candidate_email_unique": {"name": "candidate_email_unique", "columns": ["email"]}, "candidate_phone_unique": {"name": "candidate_phone_unique", "columns": ["phone"]}, "candidate_cv_path_unique": {"name": "candidate_cv_path_unique", "columns": ["cv_path"]}}, "checkConstraint": {}}, "departments": {"name": "departments", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"departments_id": {"name": "departments_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "interviews": {"name": "interviews", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "applications_id": {"name": "applications_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "locations": {"name": "locations", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "start_at": {"name": "start_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "end_at": {"name": "end_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "enum('SCHEDULE','AWAITING_FEEDBACK','COMPLETE')", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"interviews_applications_id_applications_id_fk": {"name": "interviews_applications_id_applications_id_fk", "tableFrom": "interviews", "tableTo": "applications", "columnsFrom": ["applications_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"interviews_id": {"name": "interviews_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "job_listing": {"name": "job_listing", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "salary_up_to": {"name": "salary_up_to", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "department": {"name": "department", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "organization": {"name": "organization", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "enum('OPEN','CLOSED','DRAFT','ARCHIVED','PENDING')", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'PENDING'"}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"job_listing_id": {"name": "job_listing_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "job_technologies": {"name": "job_technologies", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "job_id": {"name": "job_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "technology_id": {"name": "technology_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"job_technologies_job_id_job_listing_id_fk": {"name": "job_technologies_job_id_job_listing_id_fk", "tableFrom": "job_technologies", "tableTo": "job_listing", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "job_technologies_technology_id_technologies_id_fk": {"name": "job_technologies_technology_id_technologies_id_fk", "tableFrom": "job_technologies", "tableTo": "technologies", "columnsFrom": ["technology_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"job_technologies_id": {"name": "job_technologies_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "org_to_department": {"name": "org_to_department", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "department_id": {"name": "department_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"org_to_department_department_id_departments_id_fk": {"name": "org_to_department_department_id_departments_id_fk", "tableFrom": "org_to_department", "tableTo": "departments", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "org_to_department_organization_id_organization_clerk_id_fk": {"name": "org_to_department_organization_id_organization_clerk_id_fk", "tableFrom": "org_to_department", "tableTo": "organization", "columnsFrom": ["organization_id"], "columnsTo": ["clerk_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"org_to_department_id": {"name": "org_to_department_id", "columns": ["id"]}}, "uniqueConstraints": {"unique_org_dept": {"name": "unique_org_dept", "columns": ["department_id", "organization_id"]}}, "checkConstraint": {}}, "organization": {"name": "organization", "columns": {"clerk_id": {"name": "clerk_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "locations": {"name": "locations", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'New-York'"}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'************'"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'<EMAIL>'"}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'purple'"}, "plugins": {"name": "plugins", "type": "json", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('{\"enabled\":[],\"settings\":{}}')"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"organization_clerk_id": {"name": "organization_clerk_id", "columns": ["clerk_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "plugins": {"name": "plugins", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "config": {"name": "config", "type": "json", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('{}')"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"plugins_id": {"name": "plugins_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "scoresCards": {"name": "scoresCards", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "applications_id": {"name": "applications_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "interviews_id": {"name": "interviews_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "interviewer": {"name": "interviewer", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "overall_recommendations": {"name": "overall_recommendations", "type": "enum('DEFINITELY_NO','NO','YES','STRONG_YES','NO_DECISION')", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'NO_DECISION'"}}, "indexes": {}, "foreignKeys": {"scoresCards_applications_id_applications_id_fk": {"name": "scoresCards_applications_id_applications_id_fk", "tableFrom": "scoresCards", "tableTo": "applications", "columnsFrom": ["applications_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "scoresCards_interviews_id_interviews_id_fk": {"name": "scoresCards_interviews_id_interviews_id_fk", "tableFrom": "scoresCards", "tableTo": "interviews", "columnsFrom": ["interviews_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"scoresCards_id": {"name": "scoresCards_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "stages": {"name": "stages", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "job_id": {"name": "job_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "stage_name": {"name": "stage_name", "type": "enum('Applied','New Candidate','Screening','Phone Interview','Interview','Offer')", "primaryKey": false, "notNull": false, "autoincrement": false}, "stage_order_id": {"name": "stage_order_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "need_schedule": {"name": "need_schedule", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "assign_to": {"name": "assign_to", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"stages_job_id_job_listing_id_fk": {"name": "stages_job_id_job_listing_id_fk", "tableFrom": "stages", "tableTo": "job_listing", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"stages_id": {"name": "stages_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "technologies": {"name": "technologies", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "years_experience": {"name": "years_experience", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"technologies_id": {"name": "technologies_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "triggers": {"name": "triggers", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "action_type": {"name": "action_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "config": {"name": "config", "type": "json", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('{\"template\":\"\",\"options\":[]}')"}, "stage_id": {"name": "stage_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"triggers_id": {"name": "triggers_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "users_table": {"name": "users_table", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "age": {"name": "age", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"users_table_id": {"name": "users_table_id", "columns": ["id"]}}, "uniqueConstraints": {"users_table_email_unique": {"name": "users_table_email_unique", "columns": ["email"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}