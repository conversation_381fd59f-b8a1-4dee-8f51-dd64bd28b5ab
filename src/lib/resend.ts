import { Resend } from "resend";
// import OrganizationInviteTemplate from "@/components/emails/organization_invite_template";

export const resend = new Resend(process.env.RESEND_API_KEY);

// const send = async () => {
//     try{
//         await resend.emails.send({
//             from: '<EMAIL>',
//             to: 'cliford<PERSON><PERSON>@hotmail.com',
//             replyTo: '<EMAIL>',
//             subject: 'hello world',
//             react: OrganizationInviteTemplate()
//         });
//     }catch (e) {
//         return {error: e};
//     };
// };