import React from 'react';
import ApplicationList from "@/app/(dashboard)/applications/_components/application-list";
import {ApplicationResponseType} from "@/types";
import {auth} from "@clerk/nextjs/server";
import ExtractFileButton from "@/components/extract-file-button";
import {Plus} from "lucide-react";
import {get_applications_with_filter_action} from "@/server/actions/application_actions";

type Props = {
    searchParams: {
        [key: string]: string | string[] | undefined;
    }
};

const Page = async ({searchParams}: Props) => {
    const {orgId} = await auth();
    const {page, per_page} = await searchParams ?? {};

    const limit = typeof per_page === "string" ? parseInt(per_page) : 8;
    const offset = typeof page === "string" ? (parseInt(page) - 1) * limit : 0;
    // const locations = location ? (location as string).split(',') : undefined;

    const result = await get_applications_with_filter_action({limit, offset, organization: orgId!});
    const len = Array.isArray(result) ? result[0] : 0;
    const application = Array.isArray(result) ? result[1] : [];
    const pageCount = Math.ceil(len as number / limit);

    return (
        <div className="p-4">
            <div className="flex items-center justify-between p-4 bg-muted rounded mb-2">
                <div className="items-center flex gap-2">
                    <h1 className="text-2xl font-bold text-gray-900">APPLICATIONS</h1>
                    <span className="px-2 bg-slate-300 flex items-center justify-center rounded">{len as number}</span>
                </div>

                <div className="flex items-center gap-4">
                    <ExtractFileButton status="application"/>
                    <div className="p-1 bg-blue-300 rounded cursor-pointer hover:bg-blue-400">
                        <Plus size={18}/>
                    </div>
                </div>
            </div>

            <ApplicationList application={application as unknown as ApplicationResponseType[]} pageCount={pageCount}/>
        </div>
    )
};

export default Page;